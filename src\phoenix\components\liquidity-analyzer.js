/**
 * Phoenix v6.0 - Dynamic Liquidity Analyzer (Mandate 1)
 * 
 * Replaces static CASCADE_LIQUIDITY_THRESHOLD with adaptive
 * Dynamic Liquidity Score (DLS) system.
 */

import { EventEmitter } from 'events';

export default class LiquidityAnalyzer extends EventEmitter {
  constructor(config = {}) {
    super();
    
    this.symbol = config.symbol || 'ETHUSDT';
    this.logger = config.logger;
    
    // DLS history for percentile calculations
    this.dlsHistory = [];
    this.maxHistoryLength = 1440; // 24 hours
    
    // Current state
    this.currentDLS = 0;
    this.currentPercentile = 50;
    
    // Adaptive thresholds
    this.thresholds = {
      signalValidation: 75,    // 75th percentile for signal validation
      highConfidence: 90,      // 90th percentile for high confidence
      lowLiquidityWarning: 25  // 25th percentile for warnings
    };
    
    // Performance stats
    this.stats = {
      dlsCalculations: 0,
      signalsValidated: 0,
      signalsRejected: 0,
      startTime: Date.now()
    };
    
    this.logger?.info('liquidity_analyzer_init', {
      symbol: this.symbol,
      thresholds: this.thresholds
    });
  }

  /**
   * Analyze order book and calculate Dynamic Liquidity Score
   */
  async analyzeOrderBook(orderBookData) {
    const { bids, asks, timestamp } = orderBookData;
    
    if (!bids || !asks || bids.length === 0 || asks.length === 0) {
      return this.createAnalysisResult(0, 'INVALID_DATA');
    }
    
    try {
      // Calculate DLS components
      const depth = this.calculateDepth(bids, asks);
      const density = this.calculateDensity(bids, asks);
      const spread = this.calculateSpreadTightness(bids, asks);
      const impact = this.estimateMarketImpact(bids, asks, 10000); // $10k order
      
      // Calculate composite DLS (0-100)
      const dls = this.calculateDLS(depth, density, spread, impact);
      
      // Update history and percentile
      this.updateDLSHistory(dls);
      
      this.stats.dlsCalculations++;
      
      const analysis = this.createAnalysisResult(dls, 'SUCCESS');
      
      // Emit events for significant changes
      if (analysis.percentile >= this.thresholds.highConfidence) {
        this.emit('HIGH_LIQUIDITY_REGIME', analysis);
      } else if (analysis.percentile <= this.thresholds.lowLiquidityWarning) {
        this.emit('LOW_LIQUIDITY_WARNING', analysis);
      }
      
      this.emit('LIQUIDITY_ANALYSIS', analysis);
      
      return analysis;
      
    } catch (error) {
      this.logger?.error('liquidity_analysis_error', {
        error: error.message,
        symbol: this.symbol
      });
      return this.createAnalysisResult(0, 'ERROR');
    }
  }

  /**
   * Calculate order book depth
   */
  calculateDepth(bids, asks) {
    const bidVolume = bids.reduce((sum, [price, qty]) => sum + parseFloat(qty), 0);
    const askVolume = asks.reduce((sum, [price, qty]) => sum + parseFloat(qty), 0);
    return bidVolume + askVolume;
  }

  /**
   * Calculate order book density around mid-price
   */
  calculateDensity(bids, asks) {
    const midPrice = this.getMidPrice(bids, asks);
    const priceRange = midPrice * 0.01; // 1% range
    const upperBound = midPrice + priceRange;
    const lowerBound = midPrice - priceRange;
    
    let densityScore = 0;
    let orderCount = 0;
    
    // Count orders within 1% of mid-price
    bids.forEach(([price, qty]) => {
      const p = parseFloat(price);
      if (p >= lowerBound && p <= midPrice) {
        densityScore += parseFloat(qty);
        orderCount++;
      }
    });
    
    asks.forEach(([price, qty]) => {
      const p = parseFloat(price);
      if (p >= midPrice && p <= upperBound) {
        densityScore += parseFloat(qty);
        orderCount++;
      }
    });
    
    return orderCount > 0 ? (densityScore / orderCount) * 10 : 0;
  }

  /**
   * Calculate spread tightness
   */
  calculateSpreadTightness(bids, asks) {
    if (bids.length === 0 || asks.length === 0) return 0;
    
    const bestBid = parseFloat(bids[0][0]);
    const bestAsk = parseFloat(asks[0][0]);
    const midPrice = (bestBid + bestAsk) / 2;
    const spread = bestAsk - bestBid;
    const spreadBps = (spread / midPrice) * 10000;
    
    // Tighter spreads = higher score
    return Math.max(0, 100 - spreadBps);
  }

  /**
   * Estimate market impact for given order size
   */
  estimateMarketImpact(bids, asks, orderSizeUSD) {
    const midPrice = this.getMidPrice(bids, asks);
    const orderSizeTokens = orderSizeUSD / midPrice;
    
    let cumulativeVolume = 0;
    let weightedPrice = 0;
    
    // Walk through bids to estimate execution price
    for (const [price, qty] of bids) {
      const priceFloat = parseFloat(price);
      const qtyFloat = parseFloat(qty);
      
      if (cumulativeVolume + qtyFloat >= orderSizeTokens) {
        const remainingSize = orderSizeTokens - cumulativeVolume;
        weightedPrice += priceFloat * remainingSize;
        break;
      } else {
        weightedPrice += priceFloat * qtyFloat;
        cumulativeVolume += qtyFloat;
      }
    }
    
    if (cumulativeVolume === 0) return 100; // High impact
    
    const avgExecutionPrice = weightedPrice / orderSizeTokens;
    const impactBps = Math.abs((avgExecutionPrice - midPrice) / midPrice) * 10000;
    
    return Math.max(0, 100 - impactBps);
  }

  /**
   * Calculate composite Dynamic Liquidity Score
   */
  calculateDLS(depth, density, spread, impact) {
    // Weighted scoring algorithm
    const depthWeight = 0.30;
    const densityWeight = 0.25;
    const spreadWeight = 0.25;
    const impactWeight = 0.20;
    
    // Normalize components to 0-100 scale
    const normalizedDepth = Math.min(100, (depth / 1000) * 100);
    const normalizedDensity = Math.min(100, density);
    const normalizedSpread = Math.min(100, spread);
    const normalizedImpact = Math.min(100, impact);
    
    const dls = (
      normalizedDepth * depthWeight +
      normalizedDensity * densityWeight +
      normalizedSpread * spreadWeight +
      normalizedImpact * impactWeight
    );
    
    return Math.round(Math.max(0, Math.min(100, dls)));
  }

  /**
   * Update DLS history and calculate percentile
   */
  updateDLSHistory(dls) {
    this.currentDLS = dls;
    
    // Add to history
    this.dlsHistory.push({
      dls,
      timestamp: Date.now()
    });
    
    // Maintain history length
    if (this.dlsHistory.length > this.maxHistoryLength) {
      this.dlsHistory.shift();
    }
    
    // Calculate percentile
    this.currentPercentile = this.calculatePercentile(dls);
  }

  /**
   * Calculate percentile of current DLS
   */
  calculatePercentile(currentDLS) {
    if (this.dlsHistory.length < 10) return 50;
    
    const sortedDLS = this.dlsHistory.map(h => h.dls).sort((a, b) => a - b);
    const position = sortedDLS.findIndex(dls => dls >= currentDLS);
    
    if (position === -1) return 100;
    
    return Math.round((position / sortedDLS.length) * 100);
  }

  /**
   * Get mid-price from order book
   */
  getMidPrice(bids, asks) {
    if (bids.length === 0 || asks.length === 0) return 0;
    
    const bestBid = parseFloat(bids[0][0]);
    const bestAsk = parseFloat(asks[0][0]);
    
    return (bestBid + bestAsk) / 2;
  }

  /**
   * Create analysis result object
   */
  createAnalysisResult(dls, status) {
    const percentile = this.currentPercentile;
    const isValidForSignal = percentile >= this.thresholds.signalValidation;
    
    if (isValidForSignal) {
      this.stats.signalsValidated++;
    } else {
      this.stats.signalsRejected++;
    }
    
    return {
      dls,
      percentile,
      isValidForSignal,
      regime: this.getLiquidityRegime(percentile),
      status,
      timestamp: Date.now(),
      thresholds: this.thresholds
    };
  }

  /**
   * Determine liquidity regime
   */
  getLiquidityRegime(percentile) {
    if (percentile >= 90) return 'ULTRA_HIGH';
    if (percentile >= 75) return 'HIGH';
    if (percentile >= 50) return 'NORMAL';
    if (percentile >= 25) return 'LOW';
    return 'CRITICAL';
  }

  /**
   * Get performance statistics
   */
  getStats() {
    const uptime = Date.now() - this.stats.startTime;
    
    return {
      ...this.stats,
      uptime: Math.floor(uptime / 1000),
      currentDLS: this.currentDLS,
      currentPercentile: this.currentPercentile,
      historyLength: this.dlsHistory.length,
      validationRate: this.stats.signalsValidated / (this.stats.signalsValidated + this.stats.signalsRejected) * 100 || 0
    };
  }
}
