# SentryCoin v4.0 - Dual-Strategy Configuration
# Copy this file to .env and configure your settings

# =============================================================================
# CORE SYSTEM CONFIGURATION
# =============================================================================

# Trading Symbol (Using SPK/USDT as requested)
SYMBOL=SPKUSDT

# Exchange Configuration
EXCHANGE=binance
NODE_ENV=production

# =============================================================================
# TELEGRAM ALERTS CONFIGURATION
# =============================================================================

# Telegram Bot Configuration (Required)
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here

# Optional Telegram API (for advanced features)
TELEGRAM_API_ID=your_api_id
TELEGRAM_API_HASH=your_api_hash

# =============================================================================
# MARKET CLASSIFIER THRESHOLDS (v4.0 Core Algorithm)
# =============================================================================

# Pressure Threshold (Ask/Bid Ratio)
PRESSURE_THRESHOLD=3.0

# Liquidity Threshold (Bid Volume)
LIQUIDITY_THRESHOLD=100000

# Momentum Thresholds (Percentage)
STRONG_MOMENTUM_THRESHOLD=-0.3
WEAK_MOMENTUM_THRESHOLD=-0.1

# Order Book Analysis
ORDER_BOOK_DEPTH=50

# =============================================================================
# TRIFECTA CONVICTION TRADER (Premium Short Strategy)
# =============================================================================

# Enable/Disable Trifecta Trading
TRIFECTA_TRADING_ENABLED=true

# Position Management
TRIFECTA_MAX_POSITION=1000
TRIFECTA_STOP_LOSS=2.0
TRIFECTA_TAKE_PROFIT=5.0

# =============================================================================
# ABSORPTION SQUEEZE TRADER (Proprietary Long Strategy)
# =============================================================================

# Enable/Disable Squeeze Trading
SQUEEZE_TRADING_ENABLED=true

# Position Management
SQUEEZE_MAX_POSITION=500
SQUEEZE_STOP_LOSS=1.5
SQUEEZE_TAKE_PROFIT=3.0
SQUEEZE_TIME_EXIT=300

# =============================================================================
# TRADING SAFETY CONFIGURATION
# =============================================================================

# Paper Trading Mode (CRITICAL SAFETY)
# Set to 'false' ONLY when ready for live trading
PAPER_TRADING=true

# Cooldown System
COOLDOWN_MINUTES=5

# =============================================================================
# CLOUD STORAGE CONFIGURATION
# =============================================================================

# Storage Type: memory, azure, mongodb
STORAGE_TYPE=memory

# Azure Table Storage (if using azure)
# AZURE_STORAGE_CONNECTION_STRING=your_connection_string
# AZURE_TABLE_NAME=sentrycoin

# MongoDB (if using mongodb)
# MONGODB_URI=mongodb://localhost:27017/sentrycoin

# =============================================================================
# SYSTEM MONITORING
# =============================================================================

# Logging Level
LOG_LEVEL=info

# Web Server Port
PORT=3000

# =============================================================================
# ALGORITHM VERSION CONTROL
# =============================================================================

# Algorithm Version (for compatibility)
ALGORITHM_VERSION=v4.0

# Enable Trifecta Analysis
ENABLE_TRIFECTA=true

# =============================================================================
# DEVELOPMENT & TESTING
# =============================================================================

# Development Mode
DEV_MODE=false

# Backtest Mode
BACKTEST_MODE=false

# Mock Data (for testing when APIs are blocked)
USE_MOCK_DATA=false

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================

# Classification Update Frequency (milliseconds)
CLASSIFICATION_INTERVAL=2000

# Position Update Frequency (milliseconds)
POSITION_UPDATE_INTERVAL=1000

# Statistics Reporting Frequency (milliseconds)
STATS_REPORT_INTERVAL=300000

# =============================================================================
# RISK MANAGEMENT
# =============================================================================

# Maximum Daily Trades
MAX_DAILY_TRADES=10

# Maximum Concurrent Positions
MAX_CONCURRENT_POSITIONS=3

# Daily Loss Limit (USD)
DAILY_LOSS_LIMIT=500

# =============================================================================
# ADVANCED CONFIGURATION
# =============================================================================

# Enable Advanced Analytics
ENABLE_ANALYTICS=true

# Enable Performance Tracking
ENABLE_PERFORMANCE_TRACKING=true

# Enable Cloud Backup
ENABLE_CLOUD_BACKUP=true

# =============================================================================
# DEPLOYMENT SPECIFIC
# =============================================================================

# Deployment Environment
DEPLOYMENT_ENV=development

# Health Check Interval
HEALTH_CHECK_INTERVAL=60000

# Auto-restart on Failure
AUTO_RESTART=true
