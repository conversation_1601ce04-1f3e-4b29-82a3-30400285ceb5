# SentryCoin v4.1.1 - Market Intelligence Platform Configuration
# Copy this file to .env and configure your settings

# =============================================================================
# CORE SYSTEM CONFIGURATION
# =============================================================================

# Trading Symbol (Using SPK/USDT as validated)
SYMBOL=SPKUSDT

# Exchange Configuration
EXCHANGE=binance
NODE_ENV=production

# =============================================================================
# TELEGRAM ALERTS CONFIGURATION
# =============================================================================

# Telegram Bot Configuration (Required)
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here

# Optional Telegram API (for advanced features)
TELEGRAM_API_ID=your_api_id
TELEGRAM_API_HASH=your_api_hash

# =============================================================================
# v4.1 MARKET REGIME DETECTION THRESHOLDS
# =============================================================================

# CASCADE_HUNTER Thresholds (Distribution Phase Detection)
CASCADE_PRESSURE_THRESHOLD=3.0
CASCADE_LIQUIDITY_THRESHOLD=100000
CASCADE_MOMENTUM_THRESHOLD=-0.3

# COIL_WATCHER Thresholds (Accumulation Phase Detection)
COIL_PRESSURE_THRESHOLD=2.0
COIL_LIQUIDITY_THRESHOLD=300000
COIL_MOMENTUM_MIN=-0.1
COIL_MOMENTUM_MAX=0.1

# SHAKEOUT_DETECTOR Thresholds (Stop Hunt Phase Detection)
SHAKEOUT_PRESSURE_THRESHOLD=1.5
SHAKEOUT_LIQUIDITY_THRESHOLD=250000
SHAKEOUT_MOMENTUM_THRESHOLD=-0.5

# Order Book Analysis
ORDER_BOOK_DEPTH=50

# =============================================================================
# CASCADE_HUNTER TRADER (Active SHORT Trading)
# =============================================================================

# Enable/Disable CASCADE_HUNTER Trading
CASCADE_TRADING_ENABLED=true

# Position Management
CASCADE_MAX_POSITION=1000
CASCADE_STOP_LOSS=2.0
CASCADE_TAKE_PROFIT=5.0

# =============================================================================
# COIL_WATCHER (Alert-Only Intelligence)
# =============================================================================

# Enable/Disable COIL_WATCHER Alerts
COIL_WATCHER_ENABLED=true

# Alert Management
COIL_COOLDOWN_MINUTES=10

# =============================================================================
# SHAKEOUT_DETECTOR (Alert-Only Intelligence)
# =============================================================================

# Enable/Disable SHAKEOUT_DETECTOR Alerts
SHAKEOUT_DETECTOR_ENABLED=true

# Alert Management
SHAKEOUT_COOLDOWN_MINUTES=15

# =============================================================================
# TRADING SAFETY CONFIGURATION
# =============================================================================

# Paper Trading Mode (CRITICAL SAFETY)
# Set to 'false' ONLY when ready for live trading
PAPER_TRADING=true

# System Cooldown
COOLDOWN_MINUTES=5

# =============================================================================
# CLOUD STORAGE CONFIGURATION
# =============================================================================

# Storage Type: memory, azure, mongodb
STORAGE_TYPE=memory

# Azure Table Storage (if using azure)
# AZURE_STORAGE_CONNECTION_STRING=your_connection_string
# AZURE_TABLE_NAME=sentrycoin

# MongoDB (if using mongodb)
# MONGODB_URI=mongodb://localhost:27017/sentrycoin

# =============================================================================
# SYSTEM MONITORING
# =============================================================================

# Logging Level
LOG_LEVEL=info

# Web Server Port
PORT=3000

# =============================================================================
# v4.1 ALGORITHM CONFIGURATION
# =============================================================================

# Algorithm Version (for compatibility)
ALGORITHM_VERSION=v4.1

# Market Intelligence Features
ENABLE_REGIME_DETECTION=true
ENABLE_PROMOTER_ANALYSIS=true
ENABLE_MANIPULATION_DETECTION=true

# =============================================================================
# ADVANCED CONFIGURATION
# =============================================================================

# Price History Length (for momentum calculation)
PRICE_HISTORY_LENGTH=300

# WebSocket Reconnection Settings
WS_RECONNECT_DELAY=5000
WS_MAX_RECONNECT_ATTEMPTS=10

# Performance Monitoring
ENABLE_PERFORMANCE_METRICS=true
METRICS_COLLECTION_INTERVAL=60000

# =============================================================================
# LIVE DEPLOYMENT SETTINGS
# =============================================================================

# Live Trading Authorization (DANGER ZONE)
# LIVE_TRADING_AUTHORIZED=false

# Emergency Stop Settings
# EMERGENCY_STOP_ENABLED=true
# MAX_DAILY_LOSS=500
# MAX_CONSECUTIVE_LOSSES=3

# Position Monitoring
POSITION_MONITOR_INTERVAL=30000
ENABLE_POSITION_ALERTS=true

# =============================================================================
# PROMOTER REVERSAL PROTOCOL (Advanced)
# =============================================================================

# Enable manual reversal protocol
ENABLE_REVERSAL_PROTOCOL=true

# Reversal signal thresholds
REVERSAL_CONFIRMATION_THRESHOLD=0.2
REVERSAL_POSITION_SIZE_RATIO=0.75

# =============================================================================
# DEVELOPMENT & TESTING
# =============================================================================

# Mock Data Generation (for testing)
ENABLE_MOCK_DATA=false
MOCK_SIGNAL_FREQUENCY=300000

# Debug Logging
ENABLE_DEBUG_LOGGING=false
DEBUG_POSITION_TRACKING=false

# Testing Mode
TESTING_MODE=false
TEST_SIGNAL_GENERATION=false
