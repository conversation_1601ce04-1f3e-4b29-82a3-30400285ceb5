# SentryCoin v5.0 "Apex Predator" Configuration
# Copy this file to .env and configure your settings

# ================================
# REQUIRED SETTINGS
# ================================
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# ================================
# SYSTEM CONFIGURATION
# ================================
NODE_ENV=production
LOG_LEVEL=info

# ================================
# TRADING CONFIGURATION
# ================================
# Primary symbol for legacy strategies
SYMBOL=SPKUSDT
EXCHANGE=binance
PAPER_TRADING=true

# Strategy enablement (comma-separated list)
ENABLED_STRATEGIES=CASCADE_HUNTER,ETH_UNWIND

# ================================
# CASCADE_HUNTER STRATEGY (Legacy)
# ================================
CASCADE_TRADING_ENABLED=true
CASCADE_MAX_POSITION=1000
CASCADE_STOP_LOSS=2.0
CASCADE_TAKE_PROFIT=5.0
CASCADE_PRIORITY=7

# Signal thresholds
PRESSURE_THRESHOLD=3.0
LIQUIDITY_THRESHOLD=100000
STRONG_MOMENTUM_THRESHOLD=-0.3
WEAK_MOMENTUM_THRESHOLD=-0.1

# ================================
# ETH_UNWIND MACRO STRATEGY
# ================================
ETH_UNWIND_ENABLED=true
ETH_UNWIND_SYMBOL=ETHUSDT
ETH_UNWIND_PRIORITY=10

# Technical levels
ETH_UNWIND_SUPPORT=3600
ETH_UNWIND_RESISTANCE=3850
ETH_UNWIND_TP1=3000
ETH_UNWIND_TP2=2800

# Derivatives thresholds
ETH_UNWIND_OI_ATH=24000000000
ETH_UNWIND_FUNDING_SPIKE=0.018
ETH_UNWIND_ELR_DANGER=0.90

# On-chain thresholds
ETH_UNWIND_EXCHANGE_INFLOW=50000

# Risk management
ETH_UNWIND_MAX_POSITION=10000
ETH_UNWIND_STOP_LOSS=7.0
ETH_UNWIND_COOLDOWN_HOURS=12

# ================================
# DATA SERVICES
# ================================

# Derivatives Monitor
DERIVATIVES_MONITOR_ENABLED=true
DERIVATIVES_UPDATE_INTERVAL=300000
DERIVATIVES_SYMBOL=ETHUSDT

# API endpoints (optional - defaults provided)
BINANCE_FUTURES_API=https://fapi.binance.com
BYBIT_API=https://api.bybit.com
COINGLASS_API=https://open-api.coinglass.com

# On-Chain Monitor v2
ONCHAIN_V2_ENABLED=true
ONCHAIN_UPDATE_INTERVAL=600000
ONCHAIN_SYMBOL=ETH

# API keys for on-chain data (optional)
GLASSNODE_API=your_glassnode_api_key
CRYPTOQUANT_API=your_cryptoquant_api_key
NANSEN_API=your_nansen_api_key

# ================================
# MULTI-STRATEGY ORCHESTRATION
# ================================
ENABLE_CONFLICT_RESOLUTION=true
MAX_CONCURRENT_STRATEGIES=5
SIGNAL_TIMEOUT_MINUTES=30

# Strategy priorities (1-10, higher = more priority)
ETH_UNWIND_PRIORITY=10
BTC_MACRO_PRIORITY=9
CASCADE_HUNTER_PRIORITY=7
SPOOF_FADER_PRIORITY=5
COIL_WATCHER_PRIORITY=3
SHAKEOUT_DETECTOR_PRIORITY=3

# ================================
# RISK MANAGEMENT
# ================================
MAX_DAILY_LOSS=5000
MAX_DRAWDOWN=10000
EMERGENCY_STOP_LOSS=15000

# Global position limits
MAX_EXPOSURE_PERCENTAGE=50
MAX_ACTIVE_POSITIONS=5

# ================================
# MONITORING & ALERTING
# ================================
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_DETAILED_LOGGING=true
ENABLE_SIGNAL_VALIDATION=true

# Reporting intervals
STATS_REPORT_INTERVAL=60
ENABLE_HOURLY_REPORTS=true
ENABLE_DAILY_REPORTS=true

# Alert controls
MAX_ALERTS_PER_HOUR=20
ENABLE_SIGNAL_CONFIRMATIONS=true
TELEGRAM_ALERT_COOLDOWN=300

# ================================
# LEGACY COMPATIBILITY
# ================================
# These settings maintain compatibility with v4.x

# Trifecta Strategy (now CASCADE_HUNTER)
TRIFECTA_TRADING_ENABLED=true
TRIFECTA_MAX_POSITION=1000
TRIFECTA_STOP_LOSS=2.0
TRIFECTA_TAKE_PROFIT=5.0

# Squeeze Strategy (disabled by default)
SQUEEZE_TRADING_ENABLED=false
SQUEEZE_MAX_POSITION=0
SQUEEZE_STOP_LOSS=1.5
SQUEEZE_TAKE_PROFIT=3.0

# Signal controls
COOLDOWN_MINUTES=5
MAX_SIGNALS_PER_HOUR=20

# Cross-signal validation
ENABLE_CONFLICT_VETO=true
CONFLICT_VETO_DURATION_MILLISECONDS=5000

# ================================
# ADVANCED FEATURES
# ================================

# Whale monitoring
REQUIRE_WHALE_CONFIRMATION=false
ENABLE_WHALE_WATCHLIST=true

# Manipulation detection
ENABLE_SPOOFING_DETECTION=true
ENABLE_WASH_TRADE_DETECTION=true

# Performance optimization
ENABLE_TRAILING_STOP_LOSS=true
TRAIL_PROFIT_TRIGGER=1.5
TRAIL_DISTANCE=1.0

# Quality scaling
CASCADE_ENABLE_QUALITY_SCALING=true
CASCADE_HIGH_QUALITY_LIQUIDITY=800000
CASCADE_MEDIUM_QUALITY_LIQUIDITY=600000
CASCADE_LOW_QUALITY_LIQUIDITY=400000

# ================================
# TECHNICAL CONFIGURATION
# ================================

# Order book analysis
ORDER_BOOK_DEPTH=50
PRICE_HISTORY_LENGTH=100
MOMENTUM_PERIOD=30

# WebSocket configuration
WS_RECONNECT_DELAY=5000
WS_MAX_RECONNECT_ATTEMPTS=10
WS_PING_INTERVAL=30000

# API rate limiting
API_REQUESTS_PER_MINUTE=1200
API_TIMEOUT=30000

# Data processing
CLASSIFICATION_INTERVAL=1000
MEMORY_CLEANUP_INTERVAL=300000

# ================================
# LOGGING CONFIGURATION
# ================================
ENABLE_FILE_LOGGING=true
ENABLE_CONSOLE_LOGGING=true
LOG_DIRECTORY=./logs
MAX_LOG_FILES=30
MAX_LOG_SIZE=10485760

# ================================
# DEVELOPMENT & TESTING
# ================================
ENABLE_MOCK_DATA=false
MOCK_DATA_INTERVAL=2000
ENABLE_TEST_SIGNALS=false
TEST_SIGNAL_PROBABILITY=0.1

# ================================
# DEPLOYMENT CONFIGURATION
# ================================
PORT=3000

# Health monitoring
MEMORY_ALERT_THRESHOLD=80
CPU_ALERT_THRESHOLD=85
ERROR_RATE_THRESHOLD=5.0

# Cloud storage (optional)
ENABLE_CLOUD_STORAGE=false
CLOUD_STORAGE_PROVIDER=aws
CLOUD_STORAGE_BUCKET=sentrycoin-data

# ================================
# NOTES
# ================================
# 1. Set PAPER_TRADING=false only when ready for live trading
# 2. Start with ETH_UNWIND_ENABLED=false for initial testing
# 3. Adjust position sizes based on your risk tolerance
# 4. Monitor system performance and adjust intervals as needed
# 5. Enable cloud storage for production deployments
# 6. Use strong API keys and keep them secure
# 7. Test thoroughly in paper trading mode before going live
